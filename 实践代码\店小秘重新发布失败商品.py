#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
店小秘重新发布失败商品：自动化获取发布失败的商品ID列表
功能：监听真实API请求获取认证信息，智能计算页数，提取所有发布失败商品的ID
作者：DrissionPage学习实践
日期：2025-01-19

主要功能：
1. 打开新标签页访问店小秘发布失败商品页面
2. 监听真实API请求获取认证信息
3. 监听统计API获取发布失败商品总数
4. 智能计算页数，确保提取全部商品ID
5. 批量请求所有页面数据并提取商品ID
6. 在控制台详细输出所有商品ID
"""

from DrissionPage import Chromium
import json
import time
import math

class DianXiaoMiFailedProductExtractor:
    """
    店小秘发布失败商品提取器
    这个类专门用于从店小秘平台提取所有发布失败的商品ID
    
    工作流程：
    1. 连接浏览器并打开目标页面
    2. 监听网络请求获取认证信息
    3. 获取发布失败商品总数
    4. 智能计算需要请求的页数
    5. 批量提取所有商品ID
    6. 在控制台输出详细结果
    """
    
    def __init__(self):
        """
        初始化提取器
        设置所有必要的实例变量
        """
        self.tab = None  # 浏览器标签页对象
        self.auth_info = None  # 认证信息（包含headers和cookies）
        self.total_failed_count = 0  # 发布失败商品总数
        self.all_product_ids = []  # 存储所有提取到的商品ID
        
    def log_message(self, message):
        """
        打印日志消息到控制台
        这个方法用于统一输出格式，方便调试和监控程序运行状态
        
        参数:
            message (str): 要输出的消息内容
        """
        print(message)
        
    def start_extraction(self):
        """
        开始提取发布失败商品ID的主流程
        这是程序的入口方法，按步骤执行所有必要的操作
        
        执行步骤：
        1. 打开目标页面
        2. 获取认证信息
        3. 获取商品总数
        4. 提取所有商品ID
        5. 输出结果
        """
        try:
            # 打印程序启动信息
            self.log_message("=" * 80)
            self.log_message("🚀 店小秘重新发布失败商品提取器启动")
            self.log_message("🎯 目标：获取所有发布失败商品的ID列表")
            self.log_message("📝 功能：智能监听API请求，自动计算页数，批量提取商品ID")
            self.log_message("=" * 80)

            # 步骤1：打开新标签页并访问目标页面
            self.log_message("\n🚀 第一步：打开新标签页并访问目标页面")
            self.log_message("📋 正在连接浏览器并创建新标签页...")
            tab = self.open_target_page()
            
            if not tab:
                self.log_message("❌ 无法打开目标页面，程序终止")
                return
                
            self.tab = tab
            self.log_message("✅ 目标页面访问成功")
            
            # 步骤2：监听API请求获取认证信息
            self.log_message("\n🚀 第二步：监听API请求获取认证信息")
            self.log_message("📋 正在启动网络监听器，等待真实API请求...")
            auth_info = self.monitor_and_get_auth_info()
            
            if not auth_info:
                self.log_message("❌ 无法获取认证信息，程序终止")
                self.log_message("💡 建议：请确保页面正常加载并发起了API请求")
                return
                
            self.auth_info = auth_info
            self.log_message("✅ 认证信息获取成功")
            
            # 步骤3：获取发布失败商品总数
            self.log_message("\n🚀 第三步：获取发布失败商品总数")
            self.log_message("📋 正在监听统计API获取商品总数...")
            total_count = self.get_failed_product_count()
            
            if total_count <= 0:
                self.log_message("⚠️ 没有发现发布失败的商品")
                self.log_message("🎉 恭喜！所有商品都发布成功了")
                return
                
            self.total_failed_count = total_count
            self.log_message(f"📊 发现 {total_count} 个发布失败的商品需要处理")
            
            # 步骤4：智能计算页数并提取所有商品ID
            self.log_message("\n🚀 第四步：智能计算页数并提取所有商品ID")
            self.log_message("📋 正在根据商品总数智能计算需要请求的页数...")
            self.extract_all_failed_product_ids()
            
            # 步骤5：输出结果
            self.log_message("\n🚀 第五步：输出提取结果")
            self.output_results()
            
        except Exception as e:
            self.log_message(f"❌ 程序执行过程中发生错误：{e}")
            self.log_message("💡 建议：检查网络连接和浏览器状态后重试")
            
    def open_target_page(self):
        """
        打开新标签页并访问目标页面
        这个方法负责连接浏览器，创建新标签页，并访问店小秘的发布失败商品页面
        
        返回:
            Tab对象: 成功时返回标签页对象，失败时返回None
        """
        try:
            # 连接到9222端口的浏览器（确保浏览器已经以调试模式启动）
            browser = Chromium(9222)
            self.log_message("✅ 已成功连接到浏览器（端口9222）")

            # 新建标签页，避免影响用户当前的浏览状态
            new_tab = browser.new_tab()
            self.log_message("📄 新建空白标签页完成")

            # 激活标签页，确保后续操作在正确的标签页中进行
            new_tab.set.activate()
            self.log_message("🎯 标签页已激活")

            # 访问店小秘发布失败商品页面
            target_url = "https://www.dianxiaomi.com/web/temu/choiceTemuList/offline?dxmOfflineState=publishFail"
            self.log_message(f"🌐 正在访问目标页面：")
            self.log_message(f"    {target_url}")
            
            # 使用get方法访问页面
            new_tab.get(target_url)
            
            # 等待页面完全加载
            self.log_message("⏳ 等待页面加载完成...")
            new_tab.wait.load_start()  # 等待页面开始加载
            new_tab.wait.doc_loaded()  # 等待文档加载完成
            self.log_message("✅ 页面加载完成")
            
            return new_tab
            
        except Exception as e:
            self.log_message(f"❌ 打开目标页面时出错：{e}")
            self.log_message("💡 请确保浏览器已启动并监听9222端口")
            return None
            
    def monitor_and_get_auth_info(self):
        """
        监听API请求获取认证信息
        这个方法启动网络监听器，捕获真实的API请求，从中提取认证信息
        
        返回:
            dict: 包含认证信息的字典，失败时返回None
        """
        try:
            # 启动网络监听器 - 监听商品列表API
            list_api = "api/pddkjProduct/pageList.json"
            self.log_message("🔍 启动网络监听器...")
            self.log_message(f"🎯 监听目标API: {list_api}")
            
            # 开始监听指定的API请求
            self.tab.listen.start(targets=list_api)
            self.log_message("✅ 网络监听器已启动，正在等待API请求...")
            
            # 等待页面自动发起API请求
            self.log_message("⏳ 等待页面自动发起API请求...")
            self.log_message("💡 如果页面没有自动发起请求，程序会尝试刷新页面")
            
            # 等待并捕获API请求（超时时间30秒）
            packet = self.tab.listen.wait(timeout=30)
            
            if packet:
                self.log_message("✅ 成功捕获到API请求！")
                return self.extract_auth_info_from_packet(packet)
            else:
                self.log_message("⚠️ 未捕获到API请求，尝试手动刷新页面")
                # 刷新页面重试
                self.tab.refresh()
                self.log_message("🔄 页面已刷新，再次等待API请求...")
                time.sleep(3)  # 等待页面刷新完成
                
                # 再次尝试捕获API请求
                packet = self.tab.listen.wait(timeout=30)
                
                if packet:
                    self.log_message("✅ 刷新后成功捕获到API请求！")
                    return self.extract_auth_info_from_packet(packet)
                else:
                    self.log_message("❌ 仍未捕获到API请求")
                    self.log_message("💡 请检查页面是否正常加载，或手动进行一些操作触发API请求")
                    return None
                    
        except Exception as e:
            self.log_message(f"❌ 监听API请求时出错：{e}")
            return None

    def extract_auth_info_from_packet(self, packet):
        """
        从捕获的数据包中提取认证信息
        这个方法解析网络数据包，提取出后续API请求所需的认证头信息

        参数:
            packet: 捕获到的网络数据包对象

        返回:
            dict: 包含认证信息的字典，失败时返回None
        """
        self.log_message("\n" + "=" * 60)
        self.log_message("🔍 正在提取真实请求的认证信息")
        self.log_message("=" * 60)

        # 检查数据包是否有效
        if not packet or not packet.request:
            self.log_message("❌ 无法获取请求信息，数据包无效")
            return None

        # 提取重要的认证头信息
        # 这些头信息是后续API请求必需的
        auth_headers = {}
        important_headers = [
            'cookie',  # 用户会话信息
            'user-agent',  # 浏览器标识
            'accept',  # 接受的内容类型
            'accept-language',  # 语言偏好
            'content-type',  # 内容类型
            'origin',  # 请求来源
            'referer',  # 引用页面
            'x-requested-with'  # AJAX请求标识
        ]

        self.log_message("📋 正在提取认证头信息...")
        # 遍历重要头信息，从请求中提取
        for header_name in important_headers:
            for key, value in packet.request.headers.items():
                if key.lower() == header_name.lower():
                    auth_headers[key] = value
                    break

        # 提取POST数据（如果有的话）
        post_data = None
        if packet.request.postData:
            try:
                # 尝试解析JSON格式的POST数据
                if isinstance(packet.request.postData, dict):
                    post_data = packet.request.postData
                else:
                    post_data = json.loads(str(packet.request.postData))
                self.log_message("📝 已提取POST数据（JSON格式）")
            except:
                # 如果不是JSON格式，保存原始格式
                post_data = str(packet.request.postData)
                self.log_message("📝 已提取POST数据（原始格式）")

        self.log_message("✅ 认证信息提取完成")
        self.log_message(f"📊 提取到 {len(auth_headers)} 个认证头信息")

        # 返回认证信息字典
        return {
            'headers': auth_headers,  # 请求头
            'post_data': post_data,   # POST数据
            'url': packet.url         # 请求URL
        }

    def get_failed_product_count(self):
        """
        获取发布失败商品总数
        这个方法监听统计API，获取各种状态商品的数量，特别是发布失败的商品数量

        返回:
            int: 发布失败的商品数量，失败时返回0
        """
        try:
            # 启动监听器监听统计API
            count_api = "api/pddkjProduct/getOfflineCounts.json"
            self.log_message(f"🔍 启动监听器监听统计API: {count_api}")
            self.tab.listen.start(targets=count_api)

            # 刷新页面触发统计API请求
            self.log_message("🔄 刷新页面触发统计API请求...")
            self.tab.refresh()

            # 等待统计API响应
            self.log_message("⏳ 等待统计API响应...")
            count_packet = self.tab.listen.wait(timeout=15)

            if count_packet and count_packet.response:
                try:
                    # 解析响应数据
                    response_body = count_packet.response.body
                    if isinstance(response_body, dict):
                        response_data = response_body
                    else:
                        response_data = json.loads(response_body)

                    # 打印完整的统计API响应数据，方便调试
                    self.log_message("📊 统计API响应数据：")
                    self.log_message(json.dumps(response_data, ensure_ascii=False, indent=2))

                    # 提取发布失败商品数量
                    if response_data.get('code') == 0 and 'data' in response_data:
                        data = response_data['data']
                        publish_fail_count = data.get('publishFail', 0)

                        # 同时显示其他状态的商品数量，提供完整信息
                        draft_num = data.get('draftNum', 0)
                        wait_publish = data.get('waitPublish', 0)
                        publishing = data.get('publishing', 0)

                        self.log_message(f"📊 商品状态统计：")
                        self.log_message(f"   📝 草稿商品：{draft_num} 个")
                        self.log_message(f"   ⏳ 等待发布：{wait_publish} 个")
                        self.log_message(f"   🔄 发布中：{publishing} 个")
                        self.log_message(f"   ❌ 发布失败：{publish_fail_count} 个")

                        return publish_fail_count
                    else:
                        self.log_message("❌ 统计API响应格式异常")
                        return 0

                except Exception as e:
                    self.log_message(f"❌ 解析统计API响应失败：{e}")
                    return 0
            else:
                self.log_message("❌ 未获取到统计API响应")
                return 0

        except Exception as e:
            self.log_message(f"❌ 获取商品总数时出错：{e}")
            return 0

    def extract_all_failed_product_ids(self):
        """
        提取所有发布失败商品的ID
        这个方法根据商品总数智能计算页数，然后逐页请求数据提取商品ID
        """
        # 计算需要请求的页数（每页300条）
        page_size = 300
        total_pages = math.ceil(self.total_failed_count / page_size)

        self.log_message(f"📊 智能计算结果：")
        self.log_message(f"   📦 总商品数：{self.total_failed_count}")
        self.log_message(f"   📄 每页商品数：{page_size}")
        self.log_message(f"   📚 需要请求页数：{total_pages}")

        self.all_product_ids = []

        # 逐页请求数据
        for page_num in range(1, total_pages + 1):
            self.log_message(f"\n📡 正在请求第 {page_num}/{total_pages} 页数据...")

            page_ids = self.request_page_product_ids(page_num)
            if page_ids:
                self.all_product_ids.extend(page_ids)
                self.log_message(f"✅ 第 {page_num} 页成功提取 {len(page_ids)} 个商品ID")
            else:
                self.log_message(f"❌ 第 {page_num} 页提取失败")

            # 页面间添加延迟，避免请求过快
            if page_num < total_pages:
                self.log_message("⏳ 等待1秒后处理下一页...")
                time.sleep(1)

        self.log_message(f"\n🎯 所有页面处理完成，共提取到 {len(self.all_product_ids)} 个商品ID")

    def request_page_product_ids(self, page_num):
        """
        请求指定页面的商品ID列表
        这个方法构建表单数据，发起POST请求，解析响应并提取商品ID

        参数:
            page_num (int): 要请求的页码

        返回:
            list: 商品ID列表，失败时返回空列表
        """
        try:
            if not self.auth_info:
                self.log_message("❌ 没有认证信息，无法发起请求")
                return []

            # 构建请求数据（按照您提供的表单格式）
            form_data = {
                'shopId': '-1',
                'shopGroupId': '',
                'fullCid': '',
                'pageNo': str(page_num),
                'pageSize': '300',
                'searchType': '0',
                'searchValue': '',
                'dxmState': 'offline',
                'dxmOfflineState': 'publishFail',
                'sortName': '2',
                'sortValue': '0'
            }

            # API地址
            api_url = "https://www.dianxiaomi.com/api/pddkjProduct/pageList.json"
            headers = self.auth_info['headers'].copy()
            headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8'

            # 将表单数据转换为URL编码格式
            from urllib.parse import urlencode
            form_body = urlencode(form_data)

            # 创建唯一的响应变量名，避免多页请求时变量冲突
            response_var = f'pageResponse_{page_num}'

            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: '{form_body}'
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.{response_var} = data;
                return data;
            }})
            .catch(error => {{
                window.{response_var} = {{
                    error: error.message
                }};
                return window.{response_var};
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(3)

            # 获取响应结果
            response_data = self.tab.run_js(f'return window.{response_var};')

            # 清理JavaScript变量，避免内存泄漏
            self.tab.run_js(f'delete window.{response_var};')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 提取商品ID列表
                    product_ids = []
                    if response_json.get('code') == 0 and 'data' in response_json:
                        data = response_json['data']
                        if 'page' in data and 'list' in data['page']:
                            product_list = data['page']['list']
                            for product in product_list:
                                product_id = product.get('id')
                                if product_id:
                                    product_ids.append(product_id)

                    return product_ids

                except Exception as e:
                    self.log_message(f"❌ 第 {page_num} 页响应解析失败：{e}")
                    return []
            else:
                status_code = response_data.get('status') if response_data else 'None'
                self.log_message(f"❌ 第 {page_num} 页请求失败，状态码: {status_code}")
                return []

        except Exception as e:
            self.log_message(f"❌ 第 {page_num} 页请求异常：{e}")
            return []

    def output_results(self):
        """
        输出提取结果
        这个方法将所有提取到的商品ID以易读的格式输出到控制台
        """
        self.log_message("\n" + "=" * 80)
        self.log_message("📋 提取结果汇总")
        self.log_message("=" * 80)

        self.log_message(f"📊 发布失败商品总数：{self.total_failed_count}")
        self.log_message(f"📊 成功提取商品ID数量：{len(self.all_product_ids)}")

        # 计算提取成功率
        if self.total_failed_count > 0:
            success_rate = (len(self.all_product_ids) / self.total_failed_count) * 100
            self.log_message(f"📊 提取成功率：{success_rate:.1f}%")

        if self.all_product_ids:
            self.log_message("\n📝 所有发布失败商品ID列表：")
            self.log_message("-" * 80)

            # 分批显示商品ID，每行显示10个，方便阅读
            for i in range(0, len(self.all_product_ids), 10):
                batch = self.all_product_ids[i:i+10]
                batch_str = ', '.join(map(str, batch))
                batch_num = i // 10 + 1
                self.log_message(f"第{batch_num:3d}批: {batch_str}")

            self.log_message("-" * 80)
            self.log_message(f"✅ 所有商品ID已成功提取并显示在控制台")

            # 提供使用建议
            self.log_message("\n💡 使用建议：")
            self.log_message("   1. 可以复制上述商品ID用于批量操作")
            self.log_message("   2. 建议保存这些ID到文件中备用")
            self.log_message("   3. 可以根据这些ID进行批量重新发布操作")
        else:
            self.log_message("⚠️ 没有提取到任何商品ID")
            self.log_message("💡 可能的原因：")
            self.log_message("   1. 页面数据加载异常")
            self.log_message("   2. API请求被拦截")
            self.log_message("   3. 认证信息过期")

        self.log_message("\n🎉 程序执行完成！")
        self.log_message("📝 如需重新运行，请刷新页面后再次执行程序")

def main():
    """
    主函数
    程序的入口点，创建提取器实例并开始执行
    """
    print("🚀 正在启动店小秘重新发布失败商品提取器...")
    print("📋 请确保浏览器已启动并监听9222端口")
    print("=" * 80)

    # 创建提取器实例
    extractor = DianXiaoMiFailedProductExtractor()

    # 开始提取
    extractor.start_extraction()

if __name__ == "__main__":
    main()
