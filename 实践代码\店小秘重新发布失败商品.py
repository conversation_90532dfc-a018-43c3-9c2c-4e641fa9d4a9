#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
店小秘重新发布失败商品：自动化获取发布失败的商品ID列表
功能：监听真实API请求获取认证信息，智能计算页数，提取所有发布失败商品的ID
作者：DrissionPage学习实践
日期：2025-01-19

主要功能：
1. 打开新标签页访问店小秘发布失败商品页面
2. 监听真实API请求获取认证信息
3. 监听统计API获取发布失败商品总数
4. 智能计算页数，确保提取全部商品ID
5. 批量请求所有页面数据并提取商品ID
6. 在控制台详细输出所有商品ID
"""

from DrissionPage import Chromium
import json
import time
import math
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

class DianXiaoMiFailedProductExtractorGUI:
    """
    店小秘发布失败商品提取器GUI版本
    这个类提供图形界面，专门用于从店小秘平台提取所有发布失败的商品ID

    工作流程：
    1. 连接浏览器并打开目标页面
    2. 监听网络请求获取认证信息
    3. 获取发布失败商品总数
    4. 智能计算需要请求的页数
    5. 批量提取所有商品ID
    6. 在GUI界面显示详细结果
    """

    def __init__(self, root):
        """
        初始化GUI提取器
        设置所有必要的实例变量和GUI界面
        """
        self.root = root
        self.root.title("店小秘重新发布失败商品提取器")
        self.root.geometry("1000x700")

        # 设置全局微软雅黑字体
        self.setup_fonts()

        # 初始化变量
        self.tab = None  # 浏览器标签页对象
        self.auth_info = None  # 认证信息（包含headers和cookies）
        self.total_failed_count = 0  # 发布失败商品总数
        self.all_product_ids = []  # 存储所有提取到的商品ID
        self.is_running = False  # 程序运行状态

        # 创建GUI界面
        self.create_widgets()

    def setup_fonts(self):
        """
        设置全局微软雅黑字体
        """
        import tkinter.font as tkFont

        # 设置默认字体为微软雅黑
        default_font = tkFont.nametofont("TkDefaultFont")
        default_font.configure(family="Microsoft YaHei", size=9)

        text_font = tkFont.nametofont("TkTextFont")
        text_font.configure(family="Microsoft YaHei", size=9)

        fixed_font = tkFont.nametofont("TkFixedFont")
        fixed_font.configure(family="Microsoft YaHei", size=9)

    def create_widgets(self):
        """
        创建GUI组件
        """
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 标题
        title_label = ttk.Label(main_frame, text="店小秘重新发布失败商品提取器",
                               font=("Microsoft YaHei", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))

        # 说明文字
        desc_label = ttk.Label(main_frame,
                              text="功能：智能监听API请求，自动计算页数，批量提取所有发布失败商品的ID",
                              font=("Microsoft YaHei", 10))
        desc_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # 控制按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 开始提取按钮
        self.start_button = ttk.Button(button_frame, text="开始提取商品ID",
                                      command=self.start_extraction,
                                      style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_button = ttk.Button(button_frame, text="停止提取",
                                     command=self.stop_extraction,
                                     state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        # 清空日志按钮
        self.clear_button = ttk.Button(button_frame, text="清空日志",
                                      command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT, padx=(0, 10))

        # 复制结果按钮
        self.copy_button = ttk.Button(button_frame, text="复制所有商品ID",
                                     command=self.copy_product_ids,
                                     state=tk.DISABLED)
        self.copy_button.pack(side=tk.LEFT, padx=(0, 10))

        # 重新发布按钮
        self.republish_button = ttk.Button(button_frame, text="重新发布商品",
                                          command=self.start_republish,
                                          state=tk.DISABLED,
                                          style="Accent.TButton")
        self.republish_button.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.StringVar(value="准备就绪")
        progress_label = ttk.Label(main_frame, textvariable=self.progress_var)
        progress_label.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        self.progress_bar = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 统计信息框架
        stats_frame = ttk.LabelFrame(main_frame, text="提取统计", padding="5")
        stats_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 统计标签
        self.stats_var = tk.StringVar(value="发布失败商品总数: 0 | 成功提取: 0 | 提取成功率: 0%")
        stats_label = ttk.Label(stats_frame, textvariable=self.stats_var)
        stats_label.pack()

        # 日志显示区域
        log_label = ttk.Label(main_frame, text="提取日志：")
        log_label.grid(row=6, column=0, sticky=tk.W, pady=(0, 5))

        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(main_frame, height=25, width=100)
        self.log_text.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(7, weight=1)

    def log_message(self, message):
        """
        在GUI日志区域显示消息
        这个方法用于统一输出格式，同时在控制台和GUI界面显示

        参数:
            message (str): 要输出的消息内容
        """
        # 在GUI界面显示
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

        # 同时在控制台显示
        print(message)

    def clear_log(self):
        """
        清空日志
        """
        self.log_text.delete(1.0, tk.END)

    def copy_product_ids(self):
        """
        复制所有商品ID到剪贴板
        """
        if not self.all_product_ids:
            messagebox.showwarning("提示", "没有商品ID可以复制")
            return

        # 将商品ID列表转换为字符串
        ids_text = ', '.join(map(str, self.all_product_ids))

        # 复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(ids_text)

        messagebox.showinfo("成功", f"已复制 {len(self.all_product_ids)} 个商品ID到剪贴板")

    def start_republish(self):
        """
        开始重新发布商品
        """
        if not self.all_product_ids:
            messagebox.showwarning("提示", "没有商品ID可以重新发布，请先提取商品ID")
            return

        if self.is_running:
            messagebox.showwarning("提示", "程序正在运行中，请等待完成后再操作")
            return

        # 确认对话框
        result = messagebox.askyesno("确认重新发布",
                                   f"确定要重新发布 {len(self.all_product_ids)} 个商品吗？\n\n"
                                   f"操作说明：\n"
                                   f"• 每批最多发布300个商品\n"
                                   f"• 系统会自动分批处理\n"
                                   f"• 具有失败重试机制\n"
                                   f"• 请确保网络连接稳定")

        if not result:
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.copy_button.config(state=tk.DISABLED)
        self.republish_button.config(state=tk.DISABLED)
        self.progress_bar.start()
        self.progress_var.set("正在重新发布商品...")

        # 在新线程中运行重新发布程序
        thread = threading.Thread(target=self.run_republish)
        thread.daemon = True
        thread.start()

    def run_republish(self):
        """
        运行重新发布程序的主逻辑（在后台线程中执行）
        """
        try:
            self.log_message("\n" + "=" * 80)
            self.log_message("🚀 开始重新发布失败商品")
            self.log_message(f"📊 待发布商品总数：{len(self.all_product_ids)}")
            self.log_message("=" * 80)

            # 检查认证信息
            if not self.auth_info:
                self.log_message("❌ 没有认证信息，无法进行重新发布")
                self.log_message("💡 请先执行商品ID提取操作获取认证信息")
                self.finish_extraction()
                return

            # 逐个处理商品ID（按照抓包结果，每次只能处理一个商品）
            total_products = len(self.all_product_ids)

            self.log_message(f"📋 处理计划：")
            self.log_message(f"   📦 总商品数：{total_products}")
            self.log_message(f"   📄 处理方式：逐个商品处理")
            self.log_message(f"   � 每个商品需要执行5个步骤的完整流程")

            success_count = 0
            failed_count = 0

            # 逐个处理商品
            for product_index in range(total_products):
                if not self.is_running:
                    self.log_message("⏹️ 用户停止了重新发布程序")
                    break

                product_id = self.all_product_ids[product_index]
                current_num = product_index + 1

                self.log_message(f"\n📡 正在处理第 {current_num}/{total_products} 个商品...")
                self.log_message(f"📋 商品ID：{product_id}")
                self.progress_var.set(f"正在发布第 {current_num}/{total_products} 个商品...")

                # 重新发布当前商品
                product_success = self.republish_batch([product_id], current_num)

                if product_success:
                    success_count += 1
                    self.log_message(f"✅ 第 {current_num} 个商品重新发布成功")
                else:
                    failed_count += 1
                    self.log_message(f"❌ 第 {current_num} 个商品重新发布失败")

                # 商品间添加延迟
                if product_index < total_products - 1 and self.is_running:
                    self.log_message("⏳ 等待3秒后处理下一个商品...")
                    time.sleep(3)

            # 输出最终结果
            self.log_message("\n" + "=" * 80)
            self.log_message("📋 重新发布结果汇总")
            self.log_message("=" * 80)
            self.log_message(f"📊 总商品数：{len(self.all_product_ids)}")
            self.log_message(f"✅ 成功发布：{success_count}")
            self.log_message(f"❌ 发布失败：{failed_count}")

            if success_count > 0:
                success_rate = (success_count / len(self.all_product_ids)) * 100
                self.log_message(f"📈 发布成功率：{success_rate:.1f}%")

            if success_count == len(self.all_product_ids):
                self.log_message("🎉 所有商品重新发布成功！")
            elif success_count > 0:
                self.log_message("⚠️ 部分商品重新发布成功，请检查失败的商品")
            else:
                self.log_message("❌ 所有商品重新发布失败，请检查网络连接和认证信息")

            self.finish_extraction()

        except Exception as e:
            self.log_message(f"❌ 重新发布过程中发生错误：{e}")
            self.log_message("💡 建议：检查网络连接和浏览器状态后重试")
            self.finish_extraction()

    def republish_batch(self, batch_ids, batch_num):
        """
        重新发布一批商品
        按照完整的流程执行：价格检测 → 违禁词检测 → 商品信息检测 → 检查进度 → 批量发布

        参数:
            batch_ids (list): 要重新发布的商品ID列表
            batch_num (int): 批次号

        返回:
            bool: 成功返回True，失败返回False
        """
        max_retries = 3  # 最大重试次数
        retry_delay = 2  # 重试间隔（秒）

        for attempt in range(max_retries):
            if not self.is_running:
                return False

            try:
                self.log_message(f"🔄 第 {batch_num} 批商品发布尝试 {attempt + 1}/{max_retries}")

                # 只处理单个商品ID（按照抓包结果，每次只处理一个）
                product_id = str(batch_ids[0]) if batch_ids else ""
                if not product_id:
                    self.log_message(f"❌ 第 {batch_num} 批没有有效的商品ID")
                    return False

                self.log_message(f"📋 正在处理商品ID: {product_id}")

                # 步骤1: 价格检测
                self.log_message(f"🔍 步骤1: 执行价格检测...")
                if not self.execute_price_detection(product_id, batch_num, attempt):
                    if attempt == max_retries - 1:
                        return False
                    else:
                        self.log_message(f"⏳ {retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue

                # 步骤2: 违禁词检测
                self.log_message(f"🔍 步骤2: 执行违禁词检测...")
                if not self.execute_banned_word_check(product_id, batch_num, attempt):
                    if attempt == max_retries - 1:
                        return False
                    else:
                        self.log_message(f"⏳ {retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue

                # 步骤3: 商品信息检测
                self.log_message(f"🔍 步骤3: 执行商品信息检测...")
                check_uuid = self.execute_product_info_check(product_id, batch_num, attempt)
                if not check_uuid:
                    if attempt == max_retries - 1:
                        return False
                    else:
                        self.log_message(f"⏳ {retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue

                # 步骤4: 检查进度
                self.log_message(f"🔍 步骤4: 检查处理进度...")
                if not self.check_process_status(check_uuid, batch_num, attempt):
                    if attempt == max_retries - 1:
                        return False
                    else:
                        self.log_message(f"⏳ {retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue

                # 步骤5: 批量发布
                self.log_message(f"🚀 步骤5: 执行批量发布...")
                if not self.execute_batch_publish(product_id, batch_num, attempt):
                    if attempt == max_retries - 1:
                        return False
                    else:
                        self.log_message(f"⏳ {retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue

                self.log_message(f"✅ 第 {batch_num} 批商品重新发布成功")
                return True

            except Exception as e:
                self.log_message(f"❌ 第 {batch_num} 批处理异常：{e}")

                if attempt == max_retries - 1:
                    return False
                else:
                    self.log_message(f"⏳ {retry_delay}秒后进行第 {attempt + 2} 次重试...")
                    time.sleep(retry_delay)
                    continue

        return False

    def execute_price_detection(self, product_id, batch_num, attempt):
        """
        执行价格检测

        参数:
            product_id (str): 商品ID
            batch_num (int): 批次号
            attempt (int): 尝试次数

        返回:
            bool: 成功返回True，失败返回False
        """
        try:
            # 构建请求数据
            form_data = {
                'dxmState': 'offline',
                'platform': 'pddkj',
                'ids': product_id
            }

            # API地址
            api_url = "https://www.dianxiaomi.com/api/priceDetection/batchProtectPriceDetection.json"
            headers = self.auth_info['headers'].copy()
            headers['Content-Type'] = 'application/x-www-form-urlencoded'

            # 将表单数据转换为URL编码格式
            from urllib.parse import urlencode
            form_body = urlencode(form_data)

            # 创建唯一的响应变量名
            response_var = f'priceDetectionResponse_{batch_num}_{attempt}'

            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: '{form_body}',
                mode: 'cors',
                credentials: 'include'
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.{response_var} = data;
                return data;
            }})
            .catch(error => {{
                window.{response_var} = {{
                    error: error.message
                }};
                return window.{response_var};
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(2)

            # 获取响应结果
            response_data = self.tab.run_js(f'return window.{response_var};')

            # 清理JavaScript变量
            self.tab.run_js(f'delete window.{response_var};')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 检查响应结果
                    if response_json.get('code') == 0:
                        self.log_message(f"✅ 价格检测通过")
                        return True
                    else:
                        error_msg = response_json.get('msg', '未知错误')
                        self.log_message(f"❌ 价格检测失败：{error_msg}")
                        return False

                except Exception as e:
                    self.log_message(f"❌ 价格检测响应解析失败：{e}")
                    return False
            else:
                status_code = response_data.get('status') if response_data else 'None'
                self.log_message(f"❌ 价格检测请求失败，状态码: {status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 价格检测异常：{e}")
            return False

    def execute_banned_word_check(self, product_id, batch_num, attempt):
        """
        执行违禁词检测

        参数:
            product_id (str): 商品ID
            batch_num (int): 批次号
            attempt (int): 尝试次数

        返回:
            bool: 成功返回True，失败返回False
        """
        try:
            # 构建请求数据
            form_data = {
                'productIds': product_id,
                'platform': 'pddkj'
            }

            # API地址
            api_url = "https://www.dianxiaomi.com/api/bannedWord/batchCheckForBannedWord.json"
            headers = self.auth_info['headers'].copy()
            headers['Content-Type'] = 'application/x-www-form-urlencoded'

            # 将表单数据转换为URL编码格式
            from urllib.parse import urlencode
            form_body = urlencode(form_data)

            # 创建唯一的响应变量名
            response_var = f'bannedWordResponse_{batch_num}_{attempt}'

            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: '{form_body}',
                mode: 'cors',
                credentials: 'include'
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.{response_var} = data;
                return data;
            }})
            .catch(error => {{
                window.{response_var} = {{
                    error: error.message
                }};
                return window.{response_var};
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(2)

            # 获取响应结果
            response_data = self.tab.run_js(f'return window.{response_var};')

            # 清理JavaScript变量
            self.tab.run_js(f'delete window.{response_var};')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 检查响应结果
                    if response_json.get('code') == 0:
                        self.log_message(f"✅ 违禁词检测通过")
                        return True
                    else:
                        error_msg = response_json.get('msg', '未知错误')
                        self.log_message(f"❌ 违禁词检测失败：{error_msg}")
                        return False

                except Exception as e:
                    self.log_message(f"❌ 违禁词检测响应解析失败：{e}")
                    return False
            else:
                status_code = response_data.get('status') if response_data else 'None'
                self.log_message(f"❌ 违禁词检测请求失败，状态码: {status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 违禁词检测异常：{e}")
            return False

    def execute_product_info_check(self, product_id, batch_num, attempt):
        """
        执行商品信息检测

        参数:
            product_id (str): 商品ID
            batch_num (int): 批次号
            attempt (int): 尝试次数

        返回:
            str: 成功返回检测UUID，失败返回None
        """
        try:
            # 构建请求数据
            form_data = {
                'ids': product_id,
                'platform': 'pddkj'
            }

            # API地址
            api_url = "https://www.dianxiaomi.com/api/productCheck/batchCheckProductInfo.json"
            headers = self.auth_info['headers'].copy()
            headers['Content-Type'] = 'application/x-www-form-urlencoded'

            # 将表单数据转换为URL编码格式
            from urllib.parse import urlencode
            form_body = urlencode(form_data)

            # 创建唯一的响应变量名
            response_var = f'productCheckResponse_{batch_num}_{attempt}'

            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: '{form_body}',
                mode: 'cors',
                credentials: 'include'
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.{response_var} = data;
                return data;
            }})
            .catch(error => {{
                window.{response_var} = {{
                    error: error.message
                }};
                return window.{response_var};
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(2)

            # 获取响应结果
            response_data = self.tab.run_js(f'return window.{response_var};')

            # 清理JavaScript变量
            self.tab.run_js(f'delete window.{response_var};')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 检查响应结果
                    if response_json.get('code') == 0:
                        check_uuid = response_json.get('data')
                        if check_uuid:
                            self.log_message(f"✅ 商品信息检测启动成功，UUID: {check_uuid}")
                            return check_uuid
                        else:
                            self.log_message(f"❌ 商品信息检测返回空UUID")
                            return None
                    else:
                        error_msg = response_json.get('msg', '未知错误')
                        self.log_message(f"❌ 商品信息检测失败：{error_msg}")
                        return None

                except Exception as e:
                    self.log_message(f"❌ 商品信息检测响应解析失败：{e}")
                    return None
            else:
                status_code = response_data.get('status') if response_data else 'None'
                self.log_message(f"❌ 商品信息检测请求失败，状态码: {status_code}")
                return None

        except Exception as e:
            self.log_message(f"❌ 商品信息检测异常：{e}")
            return None

    def check_process_status(self, check_uuid, batch_num, attempt):
        """
        检查处理进度

        参数:
            check_uuid (str): 检测UUID
            batch_num (int): 批次号
            attempt (int): 尝试次数

        返回:
            bool: 成功返回True，失败返回False
        """
        try:
            # 构建请求数据
            from urllib.parse import quote
            encoded_uuid = quote(check_uuid)

            form_data = {
                'uuid': encoded_uuid
            }

            # API地址
            api_url = "https://www.dianxiaomi.com/api/checkProcess.json"
            headers = self.auth_info['headers'].copy()
            headers['Content-Type'] = 'application/x-www-form-urlencoded'

            # 将表单数据转换为URL编码格式
            from urllib.parse import urlencode
            form_body = urlencode(form_data)

            # 创建唯一的响应变量名
            response_var = f'processCheckResponse_{batch_num}_{attempt}'

            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: '{form_body}',
                mode: 'cors',
                credentials: 'include'
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.{response_var} = data;
                return data;
            }})
            .catch(error => {{
                window.{response_var} = {{
                    error: error.message
                }};
                return window.{response_var};
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(3)  # 进度检查可能需要更长时间

            # 获取响应结果
            response_data = self.tab.run_js(f'return window.{response_var};')

            # 清理JavaScript变量
            self.tab.run_js(f'delete window.{response_var};')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 检查响应结果
                    if response_json.get('code') == 0:
                        data = response_json.get('data', {})
                        process_msg = data.get('processMsg', {})

                        if process_msg.get('code') == 1:
                            msg = process_msg.get('msg', '')
                            self.log_message(f"✅ 进度检查完成：{msg}")
                            return True
                        else:
                            self.log_message(f"⏳ 进度检查未完成，继续等待...")
                            return False
                    else:
                        error_msg = response_json.get('msg', '未知错误')
                        self.log_message(f"❌ 进度检查失败：{error_msg}")
                        return False

                except Exception as e:
                    self.log_message(f"❌ 进度检查响应解析失败：{e}")
                    return False
            else:
                status_code = response_data.get('status') if response_data else 'None'
                self.log_message(f"❌ 进度检查请求失败，状态码: {status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 进度检查异常：{e}")
            return False

    def execute_batch_publish(self, product_id, batch_num, attempt):
        """
        执行批量发布

        参数:
            product_id (str): 商品ID
            batch_num (int): 批次号
            attempt (int): 尝试次数

        返回:
            bool: 成功返回True，失败返回False
        """
        try:
            # 构建请求数据
            form_data = {
                'ids': product_id,
                'shopId': '-1'
            }

            # API地址
            api_url = "https://www.dianxiaomi.com/api/pddkjProduct/batchPublish.json"
            headers = self.auth_info['headers'].copy()
            headers['Content-Type'] = 'application/x-www-form-urlencoded'

            # 将表单数据转换为URL编码格式
            from urllib.parse import urlencode
            form_body = urlencode(form_data)

            # 创建唯一的响应变量名
            response_var = f'batchPublishResponse_{batch_num}_{attempt}'

            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: '{form_body}',
                mode: 'cors',
                credentials: 'include'
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.{response_var} = data;
                return data;
            }})
            .catch(error => {{
                window.{response_var} = {{
                    error: error.message
                }};
                return window.{response_var};
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(3)

            # 获取响应结果
            response_data = self.tab.run_js(f'return window.{response_var};')

            # 清理JavaScript变量
            self.tab.run_js(f'delete window.{response_var};')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 检查响应结果
                    if response_json.get('code') == 0:
                        data = response_json.get('data', {})
                        success_list = data.get('successList', [])
                        fail_list = data.get('failList', [])

                        if success_list:
                            self.log_message(f"✅ 批量发布成功，成功数量: {len(success_list)}")
                            return True
                        elif fail_list:
                            self.log_message(f"❌ 批量发布失败，失败数量: {len(fail_list)}")
                            return False
                        else:
                            self.log_message(f"✅ 批量发布完成")
                            return True
                    else:
                        error_msg = response_json.get('msg', '未知错误')
                        self.log_message(f"❌ 批量发布失败：{error_msg}")
                        return False

                except Exception as e:
                    self.log_message(f"❌ 批量发布响应解析失败：{e}")
                    return False
            else:
                status_code = response_data.get('status') if response_data else 'None'
                self.log_message(f"❌ 批量发布请求失败，状态码: {status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 批量发布异常：{e}")
            return False

    def start_extraction(self):
        """
        开始提取商品ID
        """
        if self.is_running:
            return

        self.is_running = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.copy_button.config(state=tk.DISABLED)
        self.republish_button.config(state=tk.DISABLED)
        self.progress_bar.start()
        self.progress_var.set("正在启动提取程序...")

        # 重置统计信息
        self.stats_var.set("发布失败商品总数: 0 | 成功提取: 0 | 提取成功率: 0%")
        self.all_product_ids = []

        # 在新线程中运行提取程序
        thread = threading.Thread(target=self.run_extraction)
        thread.daemon = True
        thread.start()

    def stop_extraction(self):
        """
        停止提取
        """
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        if self.all_product_ids:
            self.copy_button.config(state=tk.NORMAL)
            self.republish_button.config(state=tk.NORMAL)
        self.progress_bar.stop()
        self.progress_var.set("已停止")
        self.log_message("⏹️ 用户手动停止了提取程序")

    def finish_extraction(self):
        """
        完成提取，重置UI状态
        """
        self.is_running = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        if self.all_product_ids:
            self.copy_button.config(state=tk.NORMAL)
            self.republish_button.config(state=tk.NORMAL)
        self.progress_bar.stop()
        self.progress_var.set("提取完成")

        # 停止监听器
        if self.tab:
            try:
                self.tab.listen.stop()
                self.log_message("✅ 网络监听器已停止")
            except:
                pass

    def update_stats(self):
        """
        更新统计信息显示
        """
        if self.total_failed_count > 0:
            success_rate = (len(self.all_product_ids) / self.total_failed_count) * 100
        else:
            success_rate = 0

        stats_text = f"发布失败商品总数: {self.total_failed_count} | 成功提取: {len(self.all_product_ids)} | 提取成功率: {success_rate:.1f}%"
        self.stats_var.set(stats_text)

    def run_extraction(self):
        """
        运行提取程序的主逻辑（在后台线程中执行）
        """
        try:
            self.log_message("=" * 80)
            self.log_message("🚀 店小秘重新发布失败商品提取器启动")
            self.log_message("🎯 目标：获取所有发布失败商品的ID列表")
            self.log_message("📝 功能：智能监听API请求，自动计算页数，批量提取商品ID")
            self.log_message("=" * 80)

            # 步骤1：打开新标签页并启动监听器
            self.progress_var.set("正在连接浏览器...")
            self.log_message("\n🚀 第一步：打开新标签页并启动监听器")
            self.log_message("📋 正在连接浏览器并创建新标签页...")
            tab = self.open_new_tab()

            if not tab or not self.is_running:
                self.log_message("❌ 无法打开新标签页，程序终止")
                self.finish_extraction()
                return

            self.tab = tab
            self.log_message("✅ 新标签页创建成功")

            # 步骤2：启动监听器并访问目标页面
            self.progress_var.set("正在监听API请求...")
            self.log_message("\n🚀 第二步：启动监听器并访问目标页面")
            self.log_message("📋 正在启动网络监听器（在访问页面之前）...")
            auth_info = self.monitor_and_visit_page()

            if not auth_info or not self.is_running:
                self.log_message("❌ 无法获取认证信息，程序终止")
                self.log_message("💡 建议：请确保页面正常加载并发起了API请求")
                self.finish_extraction()
                return

            self.auth_info = auth_info
            self.log_message("✅ 认证信息获取成功")

            # 步骤3：获取发布失败商品总数
            self.progress_var.set("正在获取商品总数...")
            self.log_message("\n🚀 第三步：获取发布失败商品总数")
            self.log_message("📋 正在监听统计API获取商品总数...")
            total_count = self.get_failed_product_count()

            if total_count <= 0 or not self.is_running:
                if total_count <= 0:
                    self.log_message("⚠️ 没有发现发布失败的商品")
                    self.log_message("🎉 恭喜！所有商品都发布成功了")
                self.finish_extraction()
                return

            self.total_failed_count = total_count
            self.update_stats()
            self.log_message(f"📊 发现 {total_count} 个发布失败的商品需要处理")

            # 步骤4：智能计算页数并提取所有商品ID
            self.progress_var.set("正在提取商品ID...")
            self.log_message("\n🚀 第四步：智能计算页数并提取所有商品ID")
            self.log_message("📋 正在根据商品总数智能计算需要请求的页数...")
            self.extract_all_failed_product_ids()

            if not self.is_running:
                self.finish_extraction()
                return

            # 步骤5：输出结果
            self.progress_var.set("正在输出结果...")
            self.log_message("\n🚀 第五步：输出提取结果")
            self.output_results()

            self.finish_extraction()

        except Exception as e:
            self.log_message(f"❌ 程序执行过程中发生错误：{e}")
            self.log_message("💡 建议：检查网络连接和浏览器状态后重试")
            self.finish_extraction()

            self.log_message("\n🚀 第四步：智能计算页数并提取所有商品ID")
            self.log_message("📋 正在根据商品总数智能计算需要请求的页数...")
            self.extract_all_failed_product_ids()
            
            # 步骤5：输出结果
            self.log_message("\n🚀 第五步：输出提取结果")
            self.output_results()
            
        except Exception as e:
            self.log_message(f"❌ 程序执行过程中发生错误：{e}")
            self.log_message("💡 建议：检查网络连接和浏览器状态后重试")
            
    def open_new_tab(self):
        """
        连接浏览器并创建新标签页
        这个方法负责连接浏览器并创建新标签页，但不访问页面

        返回:
            Tab对象: 成功时返回标签页对象，失败时返回None
        """
        try:
            # 连接到9222端口的浏览器（确保浏览器已经以调试模式启动）
            browser = Chromium(9222)
            self.log_message("✅ 已成功连接到浏览器（端口9222）")

            # 新建标签页，避免影响用户当前的浏览状态
            new_tab = browser.new_tab()
            self.log_message("📄 新建空白标签页完成")

            # 激活标签页，确保后续操作在正确的标签页中进行
            new_tab.set.activate()
            self.log_message("🎯 标签页已激活")

            return new_tab

        except Exception as e:
            self.log_message(f"❌ 创建新标签页时出错：{e}")
            self.log_message("💡 请确保浏览器已启动并监听9222端口")
            return None
            
    def monitor_and_visit_page(self):
        """
        启动监听器并访问目标页面
        这个方法先启动网络监听器，然后访问页面，确保能捕获到页面加载时的API请求

        返回:
            dict: 包含认证信息的字典，失败时返回None
        """
        try:
            # 启动网络监听器 - 监听商品列表API（在访问页面之前）
            list_api = "api/pddkjProduct/pageList.json"
            self.log_message("🔍 启动网络监听器（在访问页面之前）...")
            self.log_message(f"🎯 监听目标API: {list_api}")

            # 开始监听指定的API请求
            self.tab.listen.start(targets=list_api)
            self.log_message("✅ 网络监听器已启动")

            # 现在访问目标页面
            target_url = "https://www.dianxiaomi.com/web/temu/choiceTemuList/offline?dxmOfflineState=publishFail"
            self.log_message(f"🌐 正在访问目标页面：")
            self.log_message(f"    {target_url}")

            # 使用get方法访问页面
            self.tab.get(target_url)

            # 等待页面完全加载
            self.log_message("⏳ 等待页面加载完成...")
            self.tab.wait.load_start()  # 等待页面开始加载
            self.tab.wait.doc_loaded()  # 等待文档加载完成
            self.log_message("✅ 页面加载完成")

            # 等待并捕获API请求（页面加载时应该会自动发起）
            self.log_message("⏳ 等待页面自动发起的API请求...")
            packet = self.tab.listen.wait(timeout=15)

            if packet:
                self.log_message("✅ 成功捕获到页面加载时的API请求！")
                return self.extract_auth_info_from_packet(packet)
            else:
                self.log_message("⚠️ 未捕获到页面加载时的API请求，尝试手动刷新页面")
                # 刷新页面重试
                self.tab.refresh()
                self.log_message("🔄 页面已刷新，再次等待API请求...")
                time.sleep(3)  # 等待页面刷新完成

                # 再次尝试捕获API请求
                packet = self.tab.listen.wait(timeout=15)

                if packet:
                    self.log_message("✅ 刷新后成功捕获到API请求！")
                    return self.extract_auth_info_from_packet(packet)
                else:
                    self.log_message("❌ 仍未捕获到API请求")
                    self.log_message("💡 请检查页面是否正常加载，或手动进行一些操作触发API请求")
                    return None

        except Exception as e:
            self.log_message(f"❌ 监听API请求时出错：{e}")
            return None

    def extract_auth_info_from_packet(self, packet):
        """
        从捕获的数据包中提取认证信息
        这个方法解析网络数据包，提取出后续API请求所需的认证头信息

        参数:
            packet: 捕获到的网络数据包对象

        返回:
            dict: 包含认证信息的字典，失败时返回None
        """
        self.log_message("\n" + "=" * 60)
        self.log_message("🔍 正在提取真实请求的认证信息")
        self.log_message("=" * 60)

        # 检查数据包是否有效
        if not packet or not packet.request:
            self.log_message("❌ 无法获取请求信息，数据包无效")
            return None

        # 提取重要的认证头信息
        # 这些头信息是后续API请求必需的
        auth_headers = {}
        important_headers = [
            'cookie',  # 用户会话信息
            'user-agent',  # 浏览器标识
            'accept',  # 接受的内容类型
            'accept-language',  # 语言偏好
            'content-type',  # 内容类型
            'origin',  # 请求来源
            'referer',  # 引用页面
            'x-requested-with'  # AJAX请求标识
        ]

        self.log_message("📋 正在提取认证头信息...")
        # 遍历重要头信息，从请求中提取
        for header_name in important_headers:
            for key, value in packet.request.headers.items():
                if key.lower() == header_name.lower():
                    auth_headers[key] = value
                    break

        # 提取POST数据（如果有的话）
        post_data = None
        if packet.request.postData:
            try:
                # 尝试解析JSON格式的POST数据
                if isinstance(packet.request.postData, dict):
                    post_data = packet.request.postData
                else:
                    post_data = json.loads(str(packet.request.postData))
                self.log_message("📝 已提取POST数据（JSON格式）")
            except:
                # 如果不是JSON格式，保存原始格式
                post_data = str(packet.request.postData)
                self.log_message("📝 已提取POST数据（原始格式）")

        self.log_message("✅ 认证信息提取完成")
        self.log_message(f"📊 提取到 {len(auth_headers)} 个认证头信息")

        # 返回认证信息字典
        return {
            'headers': auth_headers,  # 请求头
            'post_data': post_data,   # POST数据
            'url': packet.url         # 请求URL
        }

    def get_failed_product_count(self):
        """
        获取发布失败商品总数
        这个方法监听统计API，获取各种状态商品的数量，特别是发布失败的商品数量

        返回:
            int: 发布失败的商品数量，失败时返回0
        """
        try:
            # 启动监听器监听统计API
            count_api = "api/pddkjProduct/getOfflineCounts.json"
            self.log_message(f"🔍 启动监听器监听统计API: {count_api}")
            self.tab.listen.start(targets=count_api)

            # 刷新页面触发统计API请求
            self.log_message("🔄 刷新页面触发统计API请求...")
            self.tab.refresh()

            # 等待统计API响应
            self.log_message("⏳ 等待统计API响应...")
            count_packet = self.tab.listen.wait(timeout=15)

            if count_packet and count_packet.response:
                try:
                    # 解析响应数据
                    response_body = count_packet.response.body
                    if isinstance(response_body, dict):
                        response_data = response_body
                    else:
                        response_data = json.loads(response_body)

                    # 打印完整的统计API响应数据，方便调试
                    self.log_message("📊 统计API响应数据：")
                    self.log_message(json.dumps(response_data, ensure_ascii=False, indent=2))

                    # 提取发布失败商品数量
                    if response_data.get('code') == 0 and 'data' in response_data:
                        data = response_data['data']
                        publish_fail_count = data.get('publishFail', 0)

                        # 同时显示其他状态的商品数量，提供完整信息
                        draft_num = data.get('draftNum', 0)
                        wait_publish = data.get('waitPublish', 0)
                        publishing = data.get('publishing', 0)

                        self.log_message(f"📊 商品状态统计：")
                        self.log_message(f"   📝 草稿商品：{draft_num} 个")
                        self.log_message(f"   ⏳ 等待发布：{wait_publish} 个")
                        self.log_message(f"   🔄 发布中：{publishing} 个")
                        self.log_message(f"   ❌ 发布失败：{publish_fail_count} 个")

                        return publish_fail_count
                    else:
                        self.log_message("❌ 统计API响应格式异常")
                        return 0

                except Exception as e:
                    self.log_message(f"❌ 解析统计API响应失败：{e}")
                    return 0
            else:
                self.log_message("❌ 未获取到统计API响应")
                return 0

        except Exception as e:
            self.log_message(f"❌ 获取商品总数时出错：{e}")
            return 0

    def extract_all_failed_product_ids(self):
        """
        提取所有发布失败商品的ID
        这个方法根据商品总数智能计算页数，然后逐页请求数据提取商品ID
        """
        # 计算需要请求的页数（每页300条）
        page_size = 300
        total_pages = math.ceil(self.total_failed_count / page_size)

        self.log_message(f"📊 智能计算结果：")
        self.log_message(f"   📦 总商品数：{self.total_failed_count}")
        self.log_message(f"   📄 每页商品数：{page_size}")
        self.log_message(f"   📚 需要请求页数：{total_pages}")

        self.all_product_ids = []

        # 逐页请求数据
        for page_num in range(1, total_pages + 1):
            # 检查是否需要停止
            if not self.is_running:
                self.log_message("⏹️ 用户停止了程序，终止提取")
                break

            self.log_message(f"\n📡 正在请求第 {page_num}/{total_pages} 页数据...")
            self.progress_var.set(f"正在提取第 {page_num}/{total_pages} 页数据...")

            page_ids = self.request_page_product_ids(page_num)
            if page_ids:
                self.all_product_ids.extend(page_ids)
                self.log_message(f"✅ 第 {page_num} 页成功提取 {len(page_ids)} 个商品ID")
                # 更新统计信息
                self.update_stats()
            else:
                self.log_message(f"❌ 第 {page_num} 页提取失败")

            # 页面间添加延迟，避免请求过快
            if page_num < total_pages and self.is_running:
                self.log_message("⏳ 等待1秒后处理下一页...")
                time.sleep(1)

        self.log_message(f"\n🎯 所有页面处理完成，共提取到 {len(self.all_product_ids)} 个商品ID")

    def request_page_product_ids(self, page_num):
        """
        请求指定页面的商品ID列表
        这个方法构建表单数据，发起POST请求，解析响应并提取商品ID

        参数:
            page_num (int): 要请求的页码

        返回:
            list: 商品ID列表，失败时返回空列表
        """
        try:
            if not self.auth_info:
                self.log_message("❌ 没有认证信息，无法发起请求")
                return []

            # 构建请求数据（按照您提供的表单格式）
            form_data = {
                'shopId': '-1',
                'shopGroupId': '',
                'fullCid': '',
                'pageNo': str(page_num),
                'pageSize': '300',
                'searchType': '0',
                'searchValue': '',
                'dxmState': 'offline',
                'dxmOfflineState': 'publishFail',
                'sortName': '2',
                'sortValue': '0'
            }

            # API地址
            api_url = "https://www.dianxiaomi.com/api/pddkjProduct/pageList.json"
            headers = self.auth_info['headers'].copy()
            headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8'

            # 将表单数据转换为URL编码格式
            from urllib.parse import urlencode
            form_body = urlencode(form_data)

            # 创建唯一的响应变量名，避免多页请求时变量冲突
            response_var = f'pageResponse_{page_num}'

            # 使用JavaScript在浏览器中发起请求
            js_code = f'''
            fetch('{api_url}', {{
                method: 'POST',
                headers: {json.dumps(headers)},
                body: '{form_body}'
            }})
            .then(response => {{
                return response.text().then(text => {{
                    return {{
                        status: response.status,
                        statusText: response.statusText,
                        body: text
                    }};
                }});
            }})
            .then(data => {{
                window.{response_var} = data;
                return data;
            }})
            .catch(error => {{
                window.{response_var} = {{
                    error: error.message
                }};
                return window.{response_var};
            }});
            '''

            # 执行JavaScript代码
            self.tab.run_js(js_code)

            # 等待请求完成
            time.sleep(3)

            # 获取响应结果
            response_data = self.tab.run_js(f'return window.{response_var};')

            # 清理JavaScript变量，避免内存泄漏
            self.tab.run_js(f'delete window.{response_var};')

            if response_data and response_data.get('status') == 200:
                try:
                    # 解析JSON响应
                    response_json = json.loads(response_data['body'])

                    # 提取商品ID列表
                    product_ids = []
                    if response_json.get('code') == 0 and 'data' in response_json:
                        data = response_json['data']
                        if 'page' in data and 'list' in data['page']:
                            product_list = data['page']['list']
                            for product in product_list:
                                product_id = product.get('id')
                                if product_id:
                                    product_ids.append(product_id)

                    return product_ids

                except Exception as e:
                    self.log_message(f"❌ 第 {page_num} 页响应解析失败：{e}")
                    return []
            else:
                status_code = response_data.get('status') if response_data else 'None'
                self.log_message(f"❌ 第 {page_num} 页请求失败，状态码: {status_code}")
                return []

        except Exception as e:
            self.log_message(f"❌ 第 {page_num} 页请求异常：{e}")
            return []

    def output_results(self):
        """
        输出提取结果
        这个方法将所有提取到的商品ID以易读的格式输出到控制台
        """
        self.log_message("\n" + "=" * 80)
        self.log_message("📋 提取结果汇总")
        self.log_message("=" * 80)

        self.log_message(f"📊 发布失败商品总数：{self.total_failed_count}")
        self.log_message(f"📊 成功提取商品ID数量：{len(self.all_product_ids)}")

        # 计算提取成功率
        if self.total_failed_count > 0:
            success_rate = (len(self.all_product_ids) / self.total_failed_count) * 100
            self.log_message(f"📊 提取成功率：{success_rate:.1f}%")

        if self.all_product_ids:
            self.log_message("\n✅ 商品ID提取完成")
            self.log_message("-" * 80)
            self.log_message(f"📊 成功提取到 {len(self.all_product_ids)} 个发布失败的商品ID")
            self.log_message("-" * 80)

            # 提供使用建议
            self.log_message("\n💡 使用建议：")
            self.log_message("   1. 点击'复制所有商品ID'按钮可复制ID到剪贴板")
            self.log_message("   2. 点击'重新发布商品'按钮可批量重新发布")
            self.log_message("   3. 建议先复制保存ID，再进行重新发布操作")
        else:
            self.log_message("⚠️ 没有提取到任何商品ID")
            self.log_message("💡 可能的原因：")
            self.log_message("   1. 页面数据加载异常")
            self.log_message("   2. API请求被拦截")
            self.log_message("   3. 认证信息过期")

        self.log_message("\n🎉 程序执行完成！")
        self.log_message("📝 如需重新运行，请刷新页面后再次执行程序")

def main():
    """
    主函数
    程序的入口点，创建GUI界面并启动应用
    """
    # 创建主窗口
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        # 可以设置窗口图标
        # root.iconbitmap('icon.ico')
        pass
    except:
        pass

    # 创建GUI应用实例
    app = DianXiaoMiFailedProductExtractorGUI(root)

    # 启动GUI主循环
    root.mainloop()

if __name__ == "__main__":
    main()
